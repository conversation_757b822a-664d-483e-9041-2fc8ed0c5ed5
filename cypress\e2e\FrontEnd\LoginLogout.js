/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import messages from '../../support/validationMessages'

import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();

describe('Deve validar o login', () => {
    beforeEach(() => {
        cy.visit(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
                'authority': 'https://qa.estrelabet.bet.br'
            },
            failOnStatusCode: false

        })
        cy.intercept({
            method: 'POST',
            url: 'https://hml.estrelabet.bet.br/next/pb/api/login',
        }).as('login')
        //cy.stubThirdParties()
        const blocked = [/ads\.mythad\.com/, /taboola\.com/, /google-analytics/]

        blocked.forEach((pattern) => {
            cy.intercept({ url: pattern }, { statusCode: 200, body: {}, log: false })
        })

    //cy.title().should('include', messages.VALIDATIONS.TITLE_PAGE.TITLE)
});

it('Validando elementos da tela', () => {
    //Valida logo do cliente
    /*  cy.get(loc.LOGIN.LOGO_CLIENTE)
         .should('be.visible')
         .and('have.attr', 'src')
         .and('include', '/assets/images/logo.svg'); 
     //Validação do título e subtítulo
     cy.validateTextContains(messages.VALIDATIONS.LOGIN_PAGE.TITLE)
     cy.validateTextContains(messages.VALIDATIONS.LOGIN_PAGE.SUBTITLE)
     //Validando campo email, título e placeholder
     cy.validateText(loc.LOGIN.EMAIL_TITULO, messages.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO)
     cy.validatePlaceholder(loc.LOGIN.EMAIL_CAMPO, messages.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL)
     //Validando campo senha, título e placeholder
     cy.validateText(loc.LOGIN.SENHA_TITULO, messages.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO)
     cy.validatePlaceholder(loc.LOGIN.SENHA_CAMPO, messages.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA)*/
    //Clicando no botão entrar
    cy.contains('button', "Entrar").click()
    // Digitar senha
    cy.get(loc.LOGIN.SENHA_CAMPO)
        .type('123456').should('have.attr', 'type', 'password'); // Verifica que está oculta
    // Clicar no botão de mostrar senha
    cy.get(loc.LOGIN.ICONE_OLHO_SENHA).should('be.visible').and('exist').click();
    // Verificar que a senha está visível
    cy.get(loc.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'text');
    // Clicar novamente para ocultar senha
    cy.get(loc.LOGIN.ICONE_OLHO_SENHA).click();
    // Verificar que a senha voltou a ser oculta
    cy.get(loc.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'password');
    //Botão esqueci a senha
    cy.validateText(loc.RECUPERAR_SENHA.BOTAO_ESQUECI_SENHA, messages.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA)
    //Botão entrar desabilitado
    cy.contains(loc.LOGIN.BOTAO_ENTRAR).should('be.disabled');
});

it.only('Deve realizar o login com sucesso', () => {
    const username = Cypress.env('user_name')
    const password = Cypress.env('user_password')
    expect(username, 'Nome de usuário').to.be.a('string').and.not.be.empty
    if (typeof password !== 'string' || !password) {
        throw new Error('O valor senha está ausente, inclua a senha usando o cy.env')
    }
    cy.clickMouse('.nebulosa-header__desktopButtonWrapper > :nth-child(2) > .nebulosa-button__root')
    cy.get(loc.LOGIN.EMAIL_CAMPO)
        .type(username)
        .should('have.value', username)
    cy.get(loc.LOGIN.SENHA_CAMPO)
        .type(password, { log: false })
        .should(el$ => {
            if (el$.val() !== password) {
                throw new Error('Valor diferente da senha digitada')
            }
        })
    cy.url().then(url => {
        cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
        cy.wait('@login').its('response.statusCode').should('eq', 200)
        cy.get('@login').then(({ request, response }) => {
            expect(request.method).to.equal('POST')
            //cy.url().should('not.eq', url);
            cy.validateText('.nebulosa-avatar__fallback:eq(2)', 'JP')
        })
    })
});

it('Deve validar login incorreto', () => {
    cy.typeText(loc.LOGIN.EMAIL_CAMPO, '<EMAIL>')
    cy.typeText(loc.LOGIN.SENHA_CAMPO, '123456')
    cy.contains(loc.LOGIN.BOTAO_ENTRAR).click()
    cy.wait('@login').its('response.statusCode').should('eq', 400)
    cy.validateTextContains(messages.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO)
});

context('Deve validar padrões de email incorreto', () => {
    it('Deve validar email no padrão incorreto', () => {
        cy.typeText(loc.LOGIN.EMAIL_CAMPO, 'qa.com.br')
        cy.get(loc.MENSAGENS_ERRO.EMAIL_INVÁLIDO)
            .should('have.text', messages.FRONT_ERRORS.LOGIN_PAGE.EMAIL_INVALIDO)
            .should('have.css', 'color')
            .and('eq', 'rgb(185, 28, 28)')
        cy.get('[for="email"]')
            .should('have.text', 'E-mail')
            .should('have.css', 'color')
            .and('eq', 'rgb(185, 28, 28)')
    });

    it('Deve validar email ausente', () => {
        cy.typeText(loc.LOGIN.SENHA_CAMPO, '123456')
        cy.contains(loc.LOGIN.BOTAO_ENTRAR).should('be.disabled');
    });
});

});
describe('Deve validar o logout', () => {
    beforeEach(() => {
        cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))
    })
    it('Deve realizar o logout', () => {
        cy.clickMouse(loc.ACESSANDO_OPÇÕES_USUÁRIO.ACESSO_MENU)
        cy.url().then(url => {
            cy.contains(loc.BOTÕES.SAIR).should('be.visible').click()
            cy.url().should('not.eq', url);
        })
    })
});