describe('{{method}} {{endpoint}}', () => {
  let bodyData;

  beforeEach(() => {
    bodyData = __BODY_DATA__;

    cy.getToken().then(() => {
      cy.authtoken();
    });
  });

  it('Deve realizar {{method}} em {{endpoint}} com sucesso', () => {
    cy.api({
      method: '{{method}}',
      url: urlRequest + '{{endpoint}}',
      headers: {
        Authorization: `Bearer ${Cypress.env('authorization')}`,
        Identity: Cypress.env('identity'),
        Authtoken: Cypress.env('authtoken')
      },
      body: bodyData
    }).then((response) => {
      {{successAssertions}}
    });
  });
 context('Cenários de erro', () => {
    {{errorTests}}
  });
});