import loc from '../support/locators'
import { faker } from '@faker-js/faker/locale/pt_BR';
import { Utility } from "../support/utility"
import messages from '../support/validationMessages'

const urlRequest = new Utility().getApiUrl();
const url = new Utility().getBaseUrl();

Cypress.Commands.add('validateText', (locator, text) => {
    cy.get(locator).scrollIntoView().should('have.text', text)
        .and('be.visible')
})

Cypress.Commands.add('validateTextContains', (text) => {
    cy.contains(text)
        .should('have.text', text)
        .and('exist')
        .and('be.visible');
});
Cypress.Commands.add('typeText', (locator, text) => {
    cy.get(locator).type(text).should('have.value', text)
})

Cypress.Commands.add('isVisible', (locator, text) => {
    cy.get(locator).should('be.visible').and('exist')
})

Cypress.Commands.add('clickMouse', (locator) => {
    cy.get(locator)
        .scrollIntoView()
        .should('be.visible')
        .click()
})

Cypress.Commands.add("login", (username, password) => {
    cy.intercept("POST", "https://hml.estrelabet.bet.br/next/pb/api/login").as("login");

    const signinPath = "/";
    const log = Cypress.log({
        name: "login",
        displayName: "LOGIN",
        message: [`🔐 Authenticating | ${username}`],
    });


    cy.visit(url)
    cy.clickMouse('.nebulosa-header__desktopButtonWrapper > :nth-child(2) > .nebulosa-button__root')
    cy.get(loc.LOGIN.EMAIL_CAMPO)
        .type(username)
        .should('have.value', username)
    cy.get(loc.LOGIN.SENHA_CAMPO)
        .type(password, { log: false })

    cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
    cy.wait('@login').its('response.statusCode').should('eq', 200)

    cy.get('.nebulosa-avatar__fallback:eq(2)', { timeout: 15000 })
        .should('have.text', 'JP')

});

Cypress.Commands.add('generatePhoneNumber', () => {
    const ddd = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    const part1 = Math.floor(Math.random() * 9000) + 1000;
    const part2 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const phoneNumber = `${ddd}9${part1}${part2}`;
    cy.wrap(phoneNumber).as('phoneNumber');
});

Cypress.Commands.add('generateCPF', () => {
    const rnd = (n) => Math.round(Math.random() * n);
    const mod = (base, div) => Math.round(base - Math.floor(base / div) * div);
    const n = Array(9).fill('').map(() => rnd(9));

    let d1 = n.reduce((total, number, index) => total + number * (10 - index), 0);
    d1 = 11 - mod(d1, 11);
    if (d1 >= 10) d1 = 0;

    let d2 = d1 * 2 + n.reduce((total, number, index) => total + number * (11 - index), 0);
    d2 = 11 - mod(d2, 11);
    if (d2 >= 10) d2 = 0;

    const cpf = `${n.join('')}${d1}${d2}`;
    return cpf;
});

Cypress.Commands.add('any', { prevSubject: 'element' }, (subject, size = 1) => {
    cy.wrap(subject).then(elementList => {
        elementList = (elementList.jquery) ? elementList.get() : elementList;
        elementList = Cypress._.sampleSize(elementList, size);
        elementList = (elementList.length > 1) ? elementList : elementList[0];
        cy.wrap(elementList);
    });
});

Cypress.Commands.add('fullName', () => {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();

    const nome = `${firstName} ${lastName}`;

    cy.wrap(nome).as('nomeCompleto');

});


Cypress.Commands.add('validatePlaceholder', (locator, text) => {
    cy.get(locator)
        .invoke('attr', 'placeholder')
        .should('to.eq', text)
        .then(() => {
            cy.get(locator)
                .should('be.visible')
                .and('exist');
        });
})

const dayjs = require('dayjs');
const localizedFormat = require('dayjs/plugin/localizedFormat');
dayjs.extend(localizedFormat);

Cypress.Commands.add('generatePastDate', () => {
    const diasAtras = Math.floor(Math.random() * 365) + 1;
    const dataPassada = dayjs().subtract(diasAtras, 'day');
    const dataFormatada = dataPassada.format('DDMMYYYY');
    return dataFormatada;
});


Cypress.Commands.add('selectRandomPageSize', (alias) => {
    // Array com os valores possíveis
    const valoresPossiveis = [10, 50, 100, 250, 500];
    // Escolha aleatoriamente um valor do array
    const valorAleatorio = Cypress._.sample(valoresPossiveis);
    // Clicar para mudar o valor de exibição
    cy.get('[role="combobox"]').click({ force: true });
    // Aguardar e clicar na opção aleatória
    cy.contains(valorAleatorio).scrollIntoView().click({ force: true });
    // Espera pela interceptação específica antes de prosseguir
    cy.wait(alias).then((intercept) => {
        expect(intercept.response.statusCode).to.eq(200);
        expect(intercept.request.url).to.include(`page=1&s=&size=${valorAleatorio}`)
            ;
    })

    cy.contains('[role="combobox"] > [style="pointer-events: none;"]', valorAleatorio)//.should('be.visible')
    // Calcular o número de registros e o número de páginas
    cy.get('.flew-row > .font-normal').invoke('text').then(text => {
        const match = text.match(/(\d+)/);
        if (match && match.length > 0) {
            const totalRegistros = parseInt(match[0]);
            const totalPages = Math.ceil(totalRegistros / valorAleatorio);
            // Verificar a quantidade de botões de página exibidos
            if (totalPages <= 10) {
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').should('have.length', totalPages);
            } else {
                // Se houver 10 ou mais páginas, verificar os primeiros 7 botões de página incluindo o ... no meio
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').should('have.length', 7);
                // Verificar se o botão "..." e o último número da página estão presentes
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').contains('...').should('be.visible');
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').contains(totalPages.toString()).should('be.visible');
            }
        }
    });
});

Cypress.Commands.add("loginToAuth0", (username, password) => {
    const log = Cypress.log({
        displayName: "AUTH0 LOGIN",
        message: [`🔐 Authenticating | ${username}`],
        autoEnd: false,
    });
    cy.intercept("POST", "https://hml.estrelabet.bet.br/next/pb/api/login").as("loginUser");

    const args = { username, password };
    cy.session(
        `auth0-${username}`,
        () => {
            cy.visit(url)
            cy.clickMouse('.nebulosa-header__desktopButtonWrapper > :nth-child(2) > .nebulosa-button__root')
            cy.get(loc.LOGIN.EMAIL_CAMPO)
                .type(username)
                .should('have.value', username)
            cy.get(loc.LOGIN.SENHA_CAMPO)
                .type(password, { log: false })

            cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
            cy.wait('@loginUser').its('response.statusCode').should('eq', 200)

            cy.get('.nebulosa-avatar__fallback:eq(2)', { timeout: 15000 })
                .should('have.text', 'JP')
        }
    );
    cy.visit(url);
    log.end()

});

Cypress.Commands.add('switchToIframe', (iframe) => {
    return cy
        .get(iframe)
        .its('0.contentDocument.body')
        .should('not.be.empty')
        .then(cy.wrap)
})

Cypress.Commands.add('getToken', (user, passwd) => {
    cy.api({
        method: 'POST',
        url: 'https://cognito-idp.us-east-1.amazonaws.com/',
        headers: {
            'content-type': 'application/x-amz-json-1.1',
            'x-amz-target': 'AWSCognitoIdentityProviderService.InitiateAuth'
        },
        body: {
            AuthFlow: "USER_PASSWORD_AUTH",
            ClientId: "4dfbu25e3cp1thk7qm6nmstleq",
            AuthParameters: {
                USERNAME: Cypress.env('user_name'),
                PASSWORD: Cypress.env('user_password')
            },
            "ClientMetadata": {}
        },
    }).its('body').should('include.keys', ['AuthenticationResult'])
        .then(responseBody => {
            const authorizationToken = responseBody.AuthenticationResult.AccessToken;
            const identityToken = responseBody.AuthenticationResult.IdToken;
            Cypress.env('authorization', authorizationToken);
            Cypress.env('identity', identityToken);

            return { authorizationToken, identityToken };
        });
});

Cypress.Commands.add('authtoken', () => {
    cy.api({
        method: 'GET',
        url: 'https://api.alfa.playprime.com.br/backoffice/api/v2/users/me',
        headers: {
            Authorization: `Bearer ${Cypress.env('authorization')}`,
            Identity: Cypress.env('identity'),
        },
    }).then(response => {
        const authtoken = response.body.token;
        Cypress.env('authtoken', authtoken);

        return { authtoken };
    });
});

Cypress.Commands.add('validateResponseTime', (response, maxTime = 1000) => {
    expect(response).to.have.property('duration')
    expect(response.duration).to.be.lessThan(
        maxTime,
        `Tempo de resposta excedeu o limite: ${response.duration}ms > ${maxTime}ms`
    )

    Cypress.log({
        name: 'validateResponseTime',
        displayName: '⏱️ Tempo de Resposta',
        message: `Duração: ${response.duration}ms`,
        consoleProps: () => {
            return {
                'Duração da Requisição (ms)': response.duration,
                'Tempo Máximo Aceito (ms)': maxTime
            }
        }
    })
})



Cypress.Commands.add('stubThirdParties', () => {
    const blocked = [
        /ads\.mythad\.com/,
        /creativecdn\.com\/tags\/v2/,
        /trc-events\.taboola\.com/,
        /unifiedPixel/,
        /taboola\.com/,
        /google\.com\/ccm\/form-data/,      // pega /pagead/form-data
        /pagead\./,                          // pega pagead.google..., gtm, etc
        /collect\?/,                         // pega analytics/gtm
        /gtm=45je56p1p3v9137/,               // pega seu pattern de GTM
        /google-analytics/,                  // pega GA clássica
        /collect\/radar/,                    // pega crm radar, etc
        // … qualquer outro domínio/padrão que aparecer
    ]

    blocked.forEach((pattern) => {
        cy.intercept(
            { url: pattern },
            { statusCode: 200, body: {}, log: false }
        )
    })
})