/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import messages from '../../support/validationMessages'
import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();

describe('Deve validar o geração de pix', () => {
    beforeEach(() => {
        cy.login(Cypress.env('user_name'), Cypress.env('user_password'))
        blockTrackers();
        //cy.title().should('include', messages.VALIDATIONS.TITLE_PAGE.TITLE)
        cy.intercept({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/deposit',
        }).as('gerandoQrCode')
    });



    it('Gerar pix com sucesso', () => {
        cy.validateText('.nebulosa-header__buttomAndBadgeWrapper > .nebulosa-button__root', 'Depositar')
        cy.clickMouse('.nebulosa-header__buttomAndBadgeWrapper > .nebulosa-button__root')

        //Validando modal de valores
        //Título
        cy.validateText('.d_flex > h1', 'Depósito')
        //Título campo e valor pré selecionado
        cy.validateText('.nebulosa-input__Root > label', 'Valor do depósito')
        cy.get('.nebulosa-input__Input > input:eq(1)').should('have.value', 'R$ 50,00')
        //Validando opções disponíveis para depósito
        cy.validateText('.d_flex > div > h3', 'Valor mínimo R$ 1,00 e valor máximo R$ 45.000,00')
        cy.validateText('[type="button"] > span:eq(0)', 'R$ 50')
        cy.validateText('[type="button"] > span:eq(1)', 'R$ 100')
        cy.validateText('[type="button"] > span:eq(2)', 'R$ 250')
        cy.validateText('[type="button"] > span:eq(3)', 'R$ 500')
        cy.validateText('[type="button"] > span:eq(4)', 'R$ 750')
        cy.validateText('[type="button"] > span:eq(5)', 'R$ 1.000')
        //validando botão e mensagem de maior de 18
        cy.validateText('[type="submit"]', 'Depositar')
        cy.validateText('form > div > .d_flex > .ai_center > p', 'Depósitos proibidos para menores de 18 anos.')
        //Botão x
        cy.isVisible('.nebulosa-modal__HeaderRight > svg')
        cy.get('img[alt="Browser Redirect"]')
            .should('have.attr', 'src', '/next/images/brands/certifieds/+18_logo.svg')
        //Banner principal
        cy.get('img[alt="Banner"]')
            .should('have.attr', 'src', 'https://hml.estrelabet.bet.br/uploads/media/EST/page-banners/ftd_banner.png')
        //clicando para avançar
        cy.clickMouse('[type="submit"]', 'Depositar')
        cy.wait('@gerandoQrCode', { timeout: 15000 })
            .its('response.statusCode')
            .should('eq', 200);
        //Label depósito/pix
        cy.validateText('.d_flex > h1', 'Depósito')
        cy.validateText('div > .d_flex > p.fs_xs:eq(1)', 'Copie o código e utilize o PIX Copia e Cola no aplicativo do seu banco.')
        cy.validateText('.flex-d_column.p_xs > .fs_xs', 'Dados bancários cadastrados')
        //Dados pessoais
        cy.validateText('.gap_nano > p.fs_xxs:eq(0)', 'Chave(s) Pix:')
        cy.validateText('.gap_nano > p.fs_xxs:eq(1)', '<EMAIL>')
        cy.validateText('.gap_nano > p.fs_xxs:eq(2)', '(11) 98206-8540')
        cy.validateText('.gap_nano > p.fs_xxs:eq(3)', '442.402.378-82')
        //Regras
        cy.validateText('.gap_md > .flex-d_column.p_xs > .gap_xxxs > .fs_xxxs', 'Pagamentos feitos por terceiros, de contas empresariais ou de bancos não cadastrados serão automaticamente rejeitados.')
        //pix copiar
        cy.isVisible('button > .d_flex > .white-space_nowrap')
        //Mostrar QRCode
        cy.validateText('.nebulosa-button__root--size_Small > .nebulosa-button__buttonLabel', 'Exibir QR Code')
        //Tempo
        cy.validateText('.d_flex > p.fs_xxxs:eq(1)', 'Tempo para realizar o pagamento')
        // Captura o valor inicial do contador
        cy.get('.pt_xxxs > .d_flex > .ai_center > div.fw_regular') 
            .invoke('text')
            .then((initialValue) => {
                // Espera 1 segundo (ou o tempo que o contador deve mudar)
                cy.wait(1000);
                // Captura novamente e compara
                cy.get('.pt_xxxs > .d_flex > .ai_center > div.fw_regular')
                    .invoke('text')
                    .should((newValue) => {
                        expect(newValue).to.not.eq(initialValue);
                    });
            });
            cy.validateText('.gap_md > .mb_xxxs > .fs_xxs', 'Valor do depósito:')
            cy.validateText('.mb_xxxs > .fs_xs', 'R$ 50,00')

    });

})