export class Utility {
 getBaseUrl() {
    let envi = Cypress.env('ENV'); // Get the value of the environment variable ENV
    if (envi == 'prod') { // Check the value
      return "https://www.estrelabet.bet.br/pb";
    } else if (envi == 'beta') {
      return "";
    } else if (envi == 'alfa') {
      return "https://hml.estrelabet.bet.br";
    }
  }

  getApiUrl() {
    let envi = Cypress.env('ENV'); // Get the value of the environment variable ENV
    if (envi == 'prod') { // Check the value
      return "";
    } else if (envi == 'beta') {
      return "";
    } else if (envi == 'alfa') {
      return "";
    }
  }
}
