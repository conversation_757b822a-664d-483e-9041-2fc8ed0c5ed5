const fs = require('fs');
const path = require('path');

const swaggerPath = path.join(__dirname, '..', 'fixtures', 'swagger.json');
const templatePath = path.join(__dirname, '..', 'fixtures', 'api-template.js');
const outputDir = path.join(__dirname, '..', 'e2e', 'Backend');

function sanitize(name) {
  return name.replace(/[{}\\/\\\\]/g, '_').replace(/^_+/, '').toLowerCase();
}

function resolveSchema(ref, swagger) {
  if (!ref?.startsWith('#/')) return {};
  const parts = ref.slice(2).split('/');
  return parts.reduce((obj, key) => obj?.[key], swagger) || {};
}

function generateMockFromSchema(schema, swagger) {
  if (schema.$ref) schema = resolveSchema(schema.$ref, swagger);
  if (schema.type === 'object') {
    const obj = {};
    for (const [key, prop] of Object.entries(schema.properties || {})) {
      obj[key] = generateMockFromSchema(prop, swagger);
    }
    return obj;
  }
  if (schema.type === 'array') {
    return [generateMockFromSchema(schema.items, swagger)];
  }
  if (schema.format === 'uuid') return '123e4567-e89b-12d3-a456-************';
  if (schema.type === 'string') return 'exemplo';
  if (schema.type === 'boolean') return true;
  if (schema.type === 'integer') return 1;
  return null;
}

function generateErrorTests(method, endpointPath) {
  if (['get', 'delete'].includes(method)) return '';

  const it1 = `
    it('Deve falhar com campo obrigatório ausente', () => {
      const invalidBodyData = { ...bodyData };
      delete invalidBodyData.campoInvalido;

      cy.api({
        method: '${method.toUpperCase()}',
        url: urlRequest + '${endpointPath}',
        failOnStatusCode: false,
        headers: {
          Authorization: 'Bearer ' + Cypress.env('authorization'),
          Identity: Cypress.env('identity'),
          Authtoken: Cypress.env('authtoken')
        },
        body: invalidBodyData
      }).then((response) => {
        expect(response.status).to.be.oneOf([400, 422]);
      });
    });
  `;

  const it2 = `
    it('Deve falhar com campo inválido', () => {
      const invalidBodyData = { ...bodyData, campoInvalido: '' };

      cy.api({
        method: '${method.toUpperCase()}',
        url: urlRequest + '${endpointPath}',
        failOnStatusCode: false,
        headers: {
          Authorization: 'Bearer ' + Cypress.env('authorization'),
          Identity: Cypress.env('identity'),
          Authtoken: Cypress.env('authtoken')
        },
        body: invalidBodyData
      }).then((response) => {
        expect(response.status).to.be.oneOf([400, 422]);
      });
    });
  `;

  return it1 + '\n' + it2;
}

async function generateTests() {
  const swagger = JSON.parse(fs.readFileSync(swaggerPath, 'utf-8'));
  const template = fs.readFileSync(templatePath, 'utf-8');

  const endpoints = Object.entries(swagger.paths || {}).flatMap(([path, methods]) =>
    Object.entries(methods).map(([method, details]) => ({
      path,
      method,
      requestBody: details.requestBody || null,
      tag: (details.tags && details.tags[0]) || 'default'
    }))
  );

  const groupedByTag = endpoints.reduce((acc, ep) => {
    const tag = sanitize(ep.tag);
    if (!acc[tag]) acc[tag] = [];
    acc[tag].push(ep);
    return acc;
  }, {});

  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });

  for (const [tag, endpoints] of Object.entries(groupedByTag)) {
    let fileContent = '';

    for (const endpoint of endpoints) {
      const method = endpoint.method.toLowerCase();
      const hasBody = ['post', 'put', 'patch'].includes(method);

      let schema = {};
      if (hasBody && endpoint.requestBody?.content?.['application/json']?.schema) {
        const ref = endpoint.requestBody.content['application/json'].schema;
        schema = ref.$ref ? resolveSchema(ref.$ref, swagger) : ref;
      }

      const bodyData = hasBody ? generateMockFromSchema(schema, swagger) : {};
      const success = "expect(response.status).to.be.oneOf([200, 201]);\n      expect(response.body).to.exist;";

      const describeBlock = template
        .replace(/{{method}}/g, endpoint.method.toUpperCase())
        .replace(/{{endpoint}}/g, endpoint.path)
        .replace('__BODY_DATA__', JSON.stringify(bodyData, null, 2))
        .replace(/{{successAssertions}}/g, success)
        .replace(/{{errorTests}}/g, generateErrorTests(method, endpoint.path));

      fileContent += `\n\n${describeBlock}`;

      console.log(`🔹 Endpoint gerado: [${endpoint.method.toUpperCase()}] ${endpoint.path}`);
    }

    const header = `
/// <reference types="cypress" />
import { Utility } from "../../support/utility"
import { faker } from '@faker-js/faker/locale/pt_BR';
import errorScenarios from '../../support/errorScenarios'

const urlRequest = new Utility().getApiUrl();`.trim();

    const finalContent = `${header}\n${fileContent}`;
    const fileName = `${tag}.js`;
    fs.writeFileSync(path.join(outputDir, fileName), finalContent.trim(), 'utf-8');
    console.log(`✅ Arquivo salvo: ${fileName}`);
  }
}

generateTests().catch(err => console.error('❌ Erro ao gerar os testes:', err.message));
