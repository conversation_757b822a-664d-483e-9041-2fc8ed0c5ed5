export function blockTrackers() {
  const blockedDomains = [
    '**ads.mythad.com/**',
    '**google-analytics.com/**',
    '**ads.mythad.com**',
    '**us.creativecdn.com**',
    '**facebook.net/**',
    '**google**'
  ];

  blockedDomains.forEach((pattern) => {
    cy.intercept(
      { method: 'GET', url: pattern },
      { statusCode: 204, body: '' },
      { log: false }
    );
    cy.intercept(
      { method: 'POST', url: pattern },
      { statusCode: 204, body: '' },
      { log: false }
    );
  });
}
