const locators = {
 LOGIN: {
        EMAIL_CAMPO: `#login`,
        EMAIL_TITULO: '[for="email"]',
        SENHA_CAMPO: '#password',
        SENHA_TITULO: '[for="password"]',
        BOTAO_ENTRAR: '.nebulosa-button__root',
        LOGO_CLIENTE: 'img[alt="main logo"]',
        ICONE_OLHO_SENHA: '.text-icon-default > svg'
    },
    BOTÕES:{
        SALVAR: '[type="submit"]',
        VOLTAR: '.row > [type="button"]',
        SAIR: 'Sair',
    },
    LISTAGENS:{
        BUSCA: '#search',
    },
    RECUPERAR_SENHA: {
        BOTAO_ESQUECI_SENHA: '.justify-end > .cursor-pointer',
        EMAIL: '[data-testid="email"]',
        SENHA: '[data-testid="password-test-id"]',
        BOTÃO: '.space-y-3 > .inline-flex',
        CONFIRMAÇÃO_SENHA: '[data-testid="confirmPassword-test-id"]',
        CÓDIGO: '[data-testid="code-test-id"]',
    },
    MENSAGENS_ERRO: {
        EMAIL_INVÁLIDO: '.h-2 > .w-full > .text-error-main',
        TOAST_EMAIL_INVÁLIDO: '.text-error-main',
        COMPLEXIDADE_SENHA_INCORRETA: '.text-error-main',
    },
    ACESSANDO_OPÇÕES_USUÁRIO: {
        ACESSO_MENU: '.rounded-lg > .w-full'
    },
    NAVBAR: {
        ADMINISTRADORES: '[href="/1b32e622-e53d-45a7-a75c-746b52686379"] > .w-full',
        USUARIO: '[href="/user/backoffice"]',
        USUARIO_APP: '[href="/users"]',
        FUNÇÃO: '[href="/role"]',
        UPLOAD: '[href="/video"]:eq(1)',
        CONVERSÃO: '[href="/videoConverter"]',
        PERFIS_DE_CONVERSÃO: '[href="/profile"]',
        CURSOS: '[href="/contents"]',
        TAG: '[href="/contents/tag"]',
        MATERIAL: '[href="/contents/materials"]',
        PLANOS: '[href="/plans"]',
        BENEFÍCIOS: '[href="/benefits"]',
        CUPONS: '[href="/plans/coupons"]',
        IMAGEM_DE_PERFIL: '[href="/user-profile-image"]',
        REDES_SOCIAIS: '[href="/social-media"]'
    }

}

export default locators;
